import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
import 'package:provider/provider.dart';

import '../../../../../core/resources/app_radius.dart';
import '../../../../../core/resources/theme/theme.dart';
import '../../../../products/view_model/products_view_model.dart';
import 'quantity_buttons.dart';

class ProductsDetailsAndQuantityButtons extends HookWidget {
  final ProductModel productModel;
  final ValueNotifier<int> quantity;
  final num totalPrice;
  final num salePrice;

  const ProductsDetailsAndQuantityButtons({
    super.key,
    required this.productModel,
    required this.quantity,
    required this.totalPrice,
    required this.salePrice,
  });

  @override
  Widget build(BuildContext context) {
    final isSale = productModel.isSale;

    final productVM = context.read<ProductVM>();

    return Row(
      children: [
        SizedBox(
          width: 120,
          height: 120,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(
              AppRadius.imageContainerRadius,
            ),
            child: Image.network(productModel.thumbnail!.url ?? ''),
          ),
        ),
        context.mediumGap,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                productVM.getProductTitle(
                        product: productModel, context: context) ??
                    '',
                style: context.labelLarge.copyWith(fontWeight: FontWeight.bold),
              ),

              context.xSmallGap,

              //! price and sale price
              FittedBox(
                fit: BoxFit.scaleDown,
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    if (isSale) ...[
                      Text(
                        salePrice.toCurrency(context),
                        style: context.title.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      context.smallGap,
                    ],
                    Text(
                      totalPrice.toCurrency(context),
                      style: isSale
                          ? context.greyLabelMedium.copyWith(
                              fontWeight: isSale ? null : FontWeight.bold,
                              decoration:
                                  isSale ? TextDecoration.lineThrough : null,
                              decorationColor: context.isDark
                                  ? ColorManager.grey.withOpacity(0.7)
                                  : ColorManager.darkGrey.withOpacity(0.5),
                            )
                          : context.title.copyWith(
                              fontWeight: isSale ? null : FontWeight.bold,
                              decoration:
                                  isSale ? TextDecoration.lineThrough : null,
                            ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        QuantityButtons(
          quantity: quantity,
        )
      ],
    );
  }
}
